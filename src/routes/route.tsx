import { SupportedLanguageType } from '@apptypes/language';
import { getPersistedQueryString } from '@features/queryParams/persistedQueryParams';
import i18n from 'i18next';
import { Location, Navigate, RouteObject } from 'react-router-dom';
import { config, getSearchParams } from 'target';

import { PAGE_IDS, ROUTES } from '../constants';
import { logError } from '../errors';

export const ROUTE_PREFIX_COLLECTION = 'RC-';
export const ROUTE_PREFIX_PAGE = 'PG-';
export const ROUTE_PREFIX_PROGRAM = 'PM-';

const knownProgramPrefixes = [ROUTE_PREFIX_COLLECTION, ROUTE_PREFIX_PAGE, ROUTE_PREFIX_PROGRAM];
const isProgramRouteWithProgramId = (programId: string) => location.pathname.includes(`${ROUTES.PROGRAM}/${programId}`);
const getVerificationRouteForProgramId = (programId: string) => `${ROUTES.VERIFICATION}/${programId}`;

export const getRoute = (programId: string): string => {
  if (!programId) {
    logError(new Error('[getRoute] missing program id'), 'WARNING');
    // TODO how do we handle these errors?
    return '';
  }

  switch (true) {
    case programId.startsWith(ROUTE_PREFIX_PROGRAM):
      return `${ROUTES.PROGRAM}/${programId.substring(3)}`;

    case isCollectionRoute(programId):
      return `${ROUTES.COLLECTION}/${programId}`;

    case programId.startsWith(ROUTE_PREFIX_PAGE):
      return `${ROUTES.PAGE}/${programId.substring(3)}`;

    default:
      // when color buttons are supported play program directly
      if (config.hasColorButtonSupport) return getVerificationRouteForProgramId(programId);

      // no color button support, if already on the program page for the current programId, play the program
      if (isProgramRouteWithProgramId(programId)) return getVerificationRouteForProgramId(programId);
      // no color button support, redirect to program page for all other cases
      return `${ROUTES.PROGRAM}/${programId}`;
  }
};

export const isCollectionRoute = (programId: string) => programId.startsWith(ROUTE_PREFIX_COLLECTION);
export const isVideoRoute = (programId: string) => !knownProgramPrefixes.find((prefix) => programId.startsWith(prefix));
export const isVideoRouteByLocation = (location: Location) => location.pathname.includes(ROUTES.VIDEO);
export const isVerificationRouteByLocation = (location: Location) => location.pathname.includes(ROUTES.VERIFICATION);
export const isTVGuideRouteByLocation = (location: Location) => location.pathname.includes(ROUTES.TV_GUIDE);

/**
 * tv guide is only supported for french and german
 */
export function currentLanguageSupportsTVGuide(lang: SupportedLanguageType) {
  return lang === 'fr' || lang === 'de';
}

export const getRouteFromQueryString = () => {
  const searchParams = getSearchParams();
  const type = searchParams.get('type');
  const id = searchParams.get('id');
  const pathFromParams = `/${type}/${id}`;

  if (id) {
    switch (type) {
      case 'player':
        return `${ROUTES.VERIFICATION}/${id}`;
      case 'page':
        if (id === PAGE_IDS.TV_GUIDE) {
          // if deep linking to tv guide, load home page if current language
          // does not support tv guide
          return currentLanguageSupportsTVGuide(i18n.language as SupportedLanguageType) ? pathFromParams : '';
        }
        return pathFromParams;
      case 'collection':
      case 'program':
        return pathFromParams;
      default:
        return '';
    }
  }
  return '';
};

export const getDeeplinkRedirectionRoute = (): RouteObject => {
  const route = getRouteFromQueryString();
  return route
    ? {
        path: '/',
        element: (
          <Navigate
            to={`${route}${getPersistedQueryString()}`}
            replace={true} /* replace true so we can't go back to the deeplink */
            state={{ isDeeplinkRedirect: true }}
          />
        ),
      }
    : {};
};
