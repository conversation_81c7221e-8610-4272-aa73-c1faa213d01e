import NoUserIcon from '@assets/img/no-user-icon.png';
import { GlobalContext } from '@providers/GlobalContextProvider';
import { isLoggedIn } from '@util/cookies';
import { FEATURE_FLAG, getFeatureFlag } from '@util/getFeatureFlag';
import React, { useContext } from 'react';

import { PAGE_IDS, PageId, TEST_ID } from '../../../constants';
import { focusClassNames, IFocusState, withFocusable } from '../../../focus';
import { Icon, IconType } from '../../Icon/Icon';
import styles from './navigation-item.module.scss';

export interface INavigationItemProperties extends IFocusState {
  label: string;
  page: PageId;
  expanded: boolean;
  testIdFocused?: boolean;
  onEnterPress: () => void;
}

const getIconForPage = (page: PageId): IconType => {
  switch (page) {
    case PAGE_IDS.SEARCH_HOME:
      return 'search';
    case PAGE_IDS.HOME:
      return 'home';
    case PAGE_IDS.ARTE_CONCERT:
      return 'concert';
    case PAGE_IDS.TV_GUIDE:
      return 'guide';
    case PAGE_IDS.MYARTE:
      return 'myarte';
    case PAGE_IDS.SETTINGS:
      return 'settings';
    case PAGE_IDS.QUIT:
      return 'exit';
    case PAGE_IDS.MY_VIDEOS:
      return 'myvideos';
    default:
      return 'home';
  }
};

const useGetImageForIcon = (page: PageId): string | undefined => {
  const { userData, featureFlags } = useContext(GlobalContext);
  const userAvatar = userData?.data ? userData?.data[0]?.avatar?.images[0]?.url : undefined;

  switch (page) {
    case PAGE_IDS.MYARTE:
      if (!getFeatureFlag(featureFlags, FEATURE_FLAG.SMARTTV_FEATURE_FLAGS_MY_ACCOUNT)) return NoUserIcon;
      return isLoggedIn() ? userAvatar || NoUserIcon : undefined;
    default:
      return undefined;
  }
};

export const NavigationItem = withFocusable(
  React.forwardRef<HTMLAnchorElement, INavigationItemProperties>(
    ({ label, page, expanded, testIdFocused, ...properties }, ref) => {
      return (
        <a
          data-testid={`${TEST_ID.MAIN_NAVIGATION}-${page.toLowerCase()}`}
          data-testid-focused={testIdFocused ? 'true' : undefined}
          data-testid-active={properties.isActive ? 'true' : undefined}
          href="#"
          ref={ref}
          className={focusClassNames(styles, properties, styles['navigation-item'], {
            [styles.expanded]: expanded,
          })}
          onClick={(event) => {
            event.preventDefault();
            properties.onEnterPress();
          }}
        >
          <Icon
            type={getIconForPage(page)}
            size="M"
            {...properties}
            className={styles.icon}
            imageSrc={useGetImageForIcon(page)}
          />
          {expanded && <h3 className={styles.label}>{label}</h3>}
        </a>
      );
    },
  ),
);
